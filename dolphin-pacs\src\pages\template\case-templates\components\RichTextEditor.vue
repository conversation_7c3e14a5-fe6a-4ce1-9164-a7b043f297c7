<template>
  <div class="rich-text-editor">
    <!-- 编辑器工具栏 -->
    <div class="editor-toolbar">
      <div class="toolbar-section">
        <el-button-group size="small">
          <el-button 
            @click="editor?.chain().focus().toggleBold().run()"
            :class="{ 'is-active': editor?.isActive('bold') }"
          >
            <el-icon><Bold /></el-icon>
          </el-button>
          <el-button 
            @click="editor?.chain().focus().toggleItalic().run()"
            :class="{ 'is-active': editor?.isActive('italic') }"
          >
            <el-icon><Italic /></el-icon>
          </el-button>
          <el-button 
            @click="editor?.chain().focus().toggleUnderline().run()"
            :class="{ 'is-active': editor?.isActive('underline') }"
          >
            <el-icon><Underline /></el-icon>
          </el-button>
          <el-button 
            @click="editor?.chain().focus().toggleStrike().run()"
            :class="{ 'is-active': editor?.isActive('strike') }"
          >
            <el-icon><Strikethrough /></el-icon>
          </el-button>
        </el-button-group>
      </div>

      <el-divider direction="vertical" />

      <div class="toolbar-section">
        <el-button-group size="small">
          <el-button 
            @click="editor?.chain().focus().toggleHeading({ level: 1 }).run()"
            :class="{ 'is-active': editor?.isActive('heading', { level: 1 }) }"
          >
            H1
          </el-button>
          <el-button 
            @click="editor?.chain().focus().toggleHeading({ level: 2 }).run()"
            :class="{ 'is-active': editor?.isActive('heading', { level: 2 }) }"
          >
            H2
          </el-button>
          <el-button 
            @click="editor?.chain().focus().toggleHeading({ level: 3 }).run()"
            :class="{ 'is-active': editor?.isActive('heading', { level: 3 }) }"
          >
            H3
          </el-button>
        </el-button-group>
      </div>

      <el-divider direction="vertical" />

      <div class="toolbar-section">
        <el-button-group size="small">
          <el-button 
            @click="editor?.chain().focus().toggleBulletList().run()"
            :class="{ 'is-active': editor?.isActive('bulletList') }"
          >
            <el-icon><List /></el-icon>
          </el-button>
          <el-button 
            @click="editor?.chain().focus().toggleOrderedList().run()"
            :class="{ 'is-active': editor?.isActive('orderedList') }"
          >
            <el-icon><Sort /></el-icon>
          </el-button>
        </el-button-group>
      </div>

      <el-divider direction="vertical" />

      <div class="toolbar-section">
        <el-button-group size="small">
          <el-button @click="insertTable">
            <el-icon><Grid /></el-icon>
            表格
          </el-button>
          <el-button @click="showVariableDialog = true">
            <el-icon><Coin /></el-icon>
            变量
          </el-button>
        </el-button-group>
      </div>

      <el-divider direction="vertical" />

      <div class="toolbar-section">
        <el-button-group size="small">
          <el-button @click="editor?.chain().focus().undo().run()">
            <el-icon><RefreshLeft /></el-icon>
          </el-button>
          <el-button @click="editor?.chain().focus().redo().run()">
            <el-icon><RefreshRight /></el-icon>
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 编辑器内容区域 -->
    <div class="editor-content">
      <EditorContent :editor="editor" />
    </div>

    <!-- 模板变量对话框 -->
    <el-dialog
      v-model="showVariableDialog"
      title="插入模板变量"
      width="500px"
    >
      <div class="variable-form">
        <el-form :model="variableForm" label-width="80px">
          <el-form-item label="变量名">
            <el-input v-model="variableForm.key" placeholder="例如：patientName" />
          </el-form-item>
          <el-form-item label="显示名">
            <el-input v-model="variableForm.label" placeholder="例如：患者姓名" />
          </el-form-item>
          <el-form-item label="类型">
            <el-select v-model="variableForm.type" style="width: 100%">
              <el-option label="文本" value="text" />
              <el-option label="数字" value="number" />
              <el-option label="日期" value="date" />
              <el-option label="图片" value="image" />
              <el-option label="布尔值" value="boolean" />
            </el-select>
          </el-form-item>
          <el-form-item label="默认值">
            <el-input v-model="variableForm.defaultValue" placeholder="默认值（可选）" />
          </el-form-item>
          <el-form-item label="必填">
            <el-switch v-model="variableForm.required" />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="showVariableDialog = false">取消</el-button>
        <el-button type="primary" @click="insertVariable">插入变量</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { useEditor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableHeader from '@tiptap/extension-table-header'
import TableCell from '@tiptap/extension-table-cell'
import Underline from '@tiptap/extension-underline'
import { ElMessage } from 'element-plus'
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Strikethrough,
  List,
  Sort,
  Grid,
  Coin,
  RefreshLeft,
  RefreshRight
} from '@element-plus/icons-vue'

import type { TemplateVariable } from '../types'

defineOptions({
  name: "RichTextEditor"
})

// Props
interface Props {
  modelValue: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: ''
})

// 事件定义
const emit = defineEmits<{
  'update:modelValue': [value: string]
  'content-updated': [content: string]
}>()

// 响应式数据
const showVariableDialog = ref(false)
const variableForm = ref<Partial<TemplateVariable>>({
  key: '',
  label: '',
  type: 'text',
  defaultValue: '',
  required: false
})

// 初始化编辑器
const editor = useEditor({
  extensions: [
    StarterKit,
    Underline,
    Table.configure({
      resizable: true,
    }),
    TableRow,
    TableHeader,
    TableCell,
  ],
  content: props.modelValue,
  onUpdate: ({ editor }) => {
    const html = editor.getHTML()
    emit('update:modelValue', html)
    emit('content-updated', html)
  },
  editorProps: {
    attributes: {
      class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
    },
  },
})

// 插入表格
const insertTable = () => {
  editor.value?.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()
}

// 插入模板变量
const insertVariable = () => {
  if (!variableForm.value.key || !variableForm.value.label) {
    ElMessage.warning('请填写变量名和显示名')
    return
  }

  const variableText = `{{${variableForm.value.key}}}`
  const variableSpan = `<span class="template-variable" data-key="${variableForm.value.key}" data-label="${variableForm.value.label}" data-type="${variableForm.value.type}">${variableText}</span>`
  
  editor.value?.chain().focus().insertContent(variableSpan).run()
  
  // 重置表单
  variableForm.value = {
    key: '',
    label: '',
    type: 'text',
    defaultValue: '',
    required: false
  }
  
  showVariableDialog.value = false
  ElMessage.success('模板变量插入成功')
}

// 监听外部内容变化
watch(() => props.modelValue, (newValue) => {
  if (editor.value && editor.value.getHTML() !== newValue) {
    editor.value.commands.setContent(newValue)
  }
})

// 生命周期
onUnmounted(() => {
  editor.value?.destroy()
})
</script>

<style lang="scss" scoped>
.rich-text-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
}

.editor-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color);
  background: var(--el-bg-color-page);
  flex-wrap: wrap;

  .toolbar-section {
    display: flex;
    align-items: center;
  }

  .el-button.is-active {
    background: var(--el-color-primary);
    color: white;
    border-color: var(--el-color-primary);
  }
}

.editor-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;

  :deep(.ProseMirror) {
    outline: none;
    min-height: 200px;

    h1, h2, h3, h4, h5, h6 {
      margin: 16px 0 8px 0;
      font-weight: 600;
    }

    h1 { font-size: 24px; }
    h2 { font-size: 20px; }
    h3 { font-size: 18px; }

    p {
      margin: 8px 0;
      line-height: 1.6;
    }

    ul, ol {
      margin: 8px 0;
      padding-left: 24px;
    }

    li {
      margin: 4px 0;
    }

    table {
      border-collapse: collapse;
      margin: 16px 0;
      width: 100%;

      td, th {
        border: 1px solid var(--el-border-color);
        padding: 8px 12px;
        text-align: left;
        vertical-align: top;
      }

      th {
        background: var(--el-bg-color-page);
        font-weight: 600;
      }
    }

    .template-variable {
      background: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
      padding: 2px 6px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 0.9em;
      border: 1px solid var(--el-color-primary-light-7);
    }
  }
}

.variable-form {
  .el-form-item {
    margin-bottom: 20px;
  }
}
</style>
