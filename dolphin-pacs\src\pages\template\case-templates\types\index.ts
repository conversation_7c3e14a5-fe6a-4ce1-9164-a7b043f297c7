// 模板编辑器相关类型定义

import type { fabric } from 'fabric'

// 基础组件类型
export type ComponentType = 
  | 'text'
  | 'textbox'
  | 'rectangle'
  | 'circle'
  | 'line'
  | 'image'
  | 'table'

// 画布对象类型
export interface CanvasObject {
  id: string
  type: ComponentType
  left: number
  top: number
  width: number
  height: number
  angle: number
  scaleX: number
  scaleY: number
  fill?: string
  stroke?: string
  strokeWidth?: number
  opacity: number
  visible: boolean
  selectable: boolean
  // Fabric.js 原始对象
  fabricObject?: fabric.Object
  // 自定义属性
  customProperties?: Record<string, any>
}

// 模板变量类型
export interface TemplateVariable {
  key: string
  label: string
  type: 'text' | 'number' | 'date' | 'image' | 'boolean'
  defaultValue: any
  required: boolean
  description?: string
  options?: string[] // 用于选择类型
}

// 画布配置
export interface CanvasConfig {
  width: number
  height: number
  backgroundColor: string
  objects: CanvasObject[]
}

// 模板内容
export interface TemplateContent {
  html: string
  variables: TemplateVariable[]
  styles?: string
}

// 模板元数据
export interface TemplateMetadata {
  category: string
  description: string
  tags?: string[]
  createdAt: Date
  updatedAt: Date
  version?: string
  author?: string
}

// 完整模板数据
export interface TemplateData {
  id: string
  name: string
  canvas: CanvasConfig
  content: TemplateContent
  metadata: TemplateMetadata
}

// 组件库项目
export interface ComponentLibraryItem {
  type: ComponentType
  name: string
  icon: string
  description: string
  defaultProps: Partial<CanvasObject>
}

// 属性面板配置
export interface PropertyConfig {
  key: string
  label: string
  type: 'input' | 'number' | 'color' | 'select' | 'switch' | 'slider'
  options?: Array<{ label: string; value: any }>
  min?: number
  max?: number
  step?: number
}

// 导出选项
export interface ExportOptions {
  format: 'pdf' | 'png' | 'jpg' | 'svg'
  quality?: number
  scale?: number
  includeCanvas?: boolean
  includeContent?: boolean
}

// 历史记录项
export interface HistoryItem {
  id: string
  timestamp: Date
  action: string
  data: any
}

// 编辑器状态
export interface EditorState {
  selectedObjects: CanvasObject[]
  clipboard: CanvasObject[]
  history: HistoryItem[]
  historyIndex: number
  zoomLevel: number
  canvasSize: { width: number; height: number }
  isDirty: boolean
}

// 拖拽数据
export interface DragData {
  type: ComponentType
  data?: any
}

// 事件类型
export interface EditorEvents {
  'object-selected': (object: CanvasObject | null) => void
  'object-modified': (object: CanvasObject) => void
  'canvas-updated': () => void
  'content-updated': (content: string) => void
  'template-saved': (template: TemplateData) => void
  'template-exported': (options: ExportOptions) => void
}
