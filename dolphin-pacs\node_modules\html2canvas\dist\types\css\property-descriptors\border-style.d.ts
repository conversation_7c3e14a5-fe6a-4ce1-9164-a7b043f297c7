import { IPropertyIdentValueDescriptor } from '../IPropertyDescriptor';
export declare const enum BORDER_STYLE {
    NONE = 0,
    SOLID = 1,
    DASHED = 2,
    DOTTED = 3,
    DOUBLE = 4
}
export declare const borderTopStyle: IPropertyIdentValueDescriptor<BORDER_STYLE>;
export declare const borderRightStyle: IPropertyIdentValueDescriptor<BORDER_STYLE>;
export declare const borderBottomStyle: IPropertyIdentValueDescriptor<BORDER_STYLE>;
export declare const borderLeftStyle: IPropertyIdentValueDescriptor<BORDER_STYLE>;
