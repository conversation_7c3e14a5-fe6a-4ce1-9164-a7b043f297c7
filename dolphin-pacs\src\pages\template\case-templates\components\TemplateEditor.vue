<template>
  <div class="template-editor">
    <!-- 顶部工具栏 -->
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <el-button @click="goBack" :icon="ArrowLeft" type="primary">
          返回
        </el-button>
        <el-divider direction="vertical" />
        <span class="template-title">{{ templateName }}</span>
      </div>

      <div class="toolbar-center">
        <el-button-group>
          <el-button @click="undo" :disabled="!canUndo" :icon="RefreshLeft">
            撤销
          </el-button>
          <el-button @click="redo" :disabled="!canRedo" :icon="RefreshRight">
            重做
          </el-button>
        </el-button-group>

        <el-divider direction="vertical" />

        <el-button-group>
          <el-button @click="zoomOut" :icon="ZoomOut">缩小</el-button>
          <el-button @click="resetZoom">{{ Math.round(zoomLevel * 100) }}%</el-button>
          <el-button @click="zoomIn" :icon="ZoomIn">放大</el-button>
        </el-button-group>
      </div>

      <div class="toolbar-right">
        <el-button @click="previewTemplate" :icon="View">
          预览
        </el-button>
        <el-button @click="saveTemplate" :icon="Document" type="success">
          保存
        </el-button>
        <el-button @click="exportTemplate" :icon="Download" type="primary">
          导出
        </el-button>
      </div>
    </div>

    <!-- 主要编辑区域 -->
    <div class="editor-main">
      <!-- 左侧组件库 -->
      <div class="editor-sidebar left">
        <ComponentLibrary @add-component="handleAddComponent" />
      </div>

      <!-- 中间画布区域 -->
      <div class="editor-canvas-container">
        <div class="canvas-wrapper">
          <TemplateCanvas
            ref="templateCanvasRef"
            :zoom-level="zoomLevel"
            @object-selected="handleObjectSelected"
            @canvas-updated="handleCanvasUpdated"
          />
        </div>

        <!-- 富文本编辑器 -->
        <div class="rich-text-section">
          <RichTextEditor
            ref="richTextEditorRef"
            v-model="templateContent"
            @content-updated="handleContentUpdated"
          />
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="editor-sidebar right">
        <PropertyPanel
          :selected-object="selectedObject"
          @property-changed="handlePropertyChanged"
          @duplicate-object="handleDuplicateObject"
          @delete-object="handleDeleteObject"
        />
      </div>
    </div>

    <!-- 预览对话框 -->
    <PreviewDialog
      v-model="showPreview"
      :template-data="currentTemplate"
    />

    <!-- 导出对话框 -->
    <ExportDialog
      v-model="showExport"
      :template-data="currentTemplate"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  RefreshLeft,
  RefreshRight,
  ZoomOut,
  ZoomIn,
  View,
  Document,
  Download
} from '@element-plus/icons-vue'

// 导入组件
import ComponentLibrary from './ComponentLibrary.vue'
import TemplateCanvas from './TemplateCanvas.vue'
import RichTextEditor from './RichTextEditor.vue'
import PropertyPanel from './PropertyPanel.vue'
import PreviewDialog from './PreviewDialog.vue'
import ExportDialog from './ExportDialog.vue'

// 导入类型
import type { TemplateData, ComponentType, CanvasObject } from '../types'

defineOptions({
  name: "TemplateEditor"
})

const route = useRoute()
const router = useRouter()

// 模板基础信息
const templateId = route.params.id as string
const templateName = ref(route.query.name as string || '新建模板')

// 编辑器状态
const zoomLevel = ref(1)
const canUndo = ref(false)
const canRedo = ref(false)
const selectedObject = ref<CanvasObject | null>(null)
const templateContent = ref('')

// 对话框状态
const showPreview = ref(false)
const showExport = ref(false)

// 组件引用
const templateCanvasRef = ref()
const richTextEditorRef = ref()

// 当前模板数据
const currentTemplate = computed<TemplateData>(() => ({
  id: templateId,
  name: templateName.value,
  canvas: {
    width: 800,
    height: 600,
    objects: templateCanvasRef.value?.getCanvasObjects() || []
  },
  content: {
    html: templateContent.value,
    variables: []
  },
  metadata: {
    category: '自定义',
    description: '',
    createdAt: new Date(),
    updatedAt: new Date()
  }
}))

// 工具栏操作
const goBack = () => {
  router.push({ name: 'CaseTemplates' })
}

const undo = () => {
  templateCanvasRef.value?.undo()
}

const redo = () => {
  templateCanvasRef.value?.redo()
}

const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value * 1.2, 3)
}

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value / 1.2, 0.1)
}

const resetZoom = () => {
  zoomLevel.value = 1
}

const previewTemplate = () => {
  showPreview.value = true
}

const saveTemplate = async () => {
  try {
    // TODO: 实现保存逻辑
    ElMessage.success('模板保存成功')
  } catch (error) {
    ElMessage.error('模板保存失败')
  }
}

const exportTemplate = () => {
  showExport.value = true
}

// 事件处理
const handleAddComponent = (componentType: ComponentType, position: { x: number, y: number }) => {
  templateCanvasRef.value?.addComponent(componentType, position)
}

const handleObjectSelected = (object: CanvasObject | null) => {
  selectedObject.value = object
}

const handleCanvasUpdated = () => {
  // 更新撤销/重做状态
  canUndo.value = templateCanvasRef.value?.canUndo() || false
  canRedo.value = templateCanvasRef.value?.canRedo() || false
}

const handleContentUpdated = (content: string) => {
  templateContent.value = content
}

const handlePropertyChanged = (property: string, value: any) => {
  templateCanvasRef.value?.updateObjectProperty(property, value)
}

const handleDuplicateObject = () => {
  templateCanvasRef.value?.duplicateSelected()
}

const handleDeleteObject = () => {
  templateCanvasRef.value?.deleteSelected()
}

// 生命周期
onMounted(() => {
  // 初始化编辑器
  console.log('模板编辑器已加载')
})

onUnmounted(() => {
  // 清理资源
})
</script>

<style lang="scss" scoped>
.template-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
}

.editor-toolbar {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  background: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .toolbar-left,
  .toolbar-center,
  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .template-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.editor-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.editor-sidebar {
  width: 280px;
  background: var(--el-bg-color-page);
  border-right: 1px solid var(--el-border-color);
  overflow-y: auto;

  &.right {
    border-right: none;
    border-left: 1px solid var(--el-border-color);
  }
}

.editor-canvas-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.canvas-wrapper {
  flex: 1;
  position: relative;
  background: #f5f5f5;
  overflow: hidden;
}

.rich-text-section {
  height: 300px;
  border-top: 1px solid var(--el-border-color);
  background: var(--el-bg-color);
}
</style>