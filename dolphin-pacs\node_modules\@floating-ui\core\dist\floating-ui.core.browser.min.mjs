const t=["top","right","bottom","left"],e=["start","end"],n=t.reduce(((t,n)=>t.concat(n,n+"-"+e[0],n+"-"+e[1])),[]),i=Math.min,r=Math.max,o={left:"right",right:"left",bottom:"top",top:"bottom"},a={start:"end",end:"start"};function l(t,e,n){return r(t,i(e,n))}function s(t,e){return"function"==typeof t?t(e):t}function c(t){return t.split("-")[0]}function f(t){return t.split("-")[1]}function m(t){return"x"===t?"y":"x"}function u(t){return"y"===t?"height":"width"}const g=new Set(["top","bottom"]);function d(t){return g.has(c(t))?"y":"x"}function p(t){return m(d(t))}function h(t,e,n){void 0===n&&(n=!1);const i=f(t),r=p(t),o=u(r);let a="x"===r?i===(n?"end":"start")?"right":"left":"start"===i?"bottom":"top";return e.reference[o]>e.floating[o]&&(a=R(a)),[a,R(a)]}function y(t){return t.replace(/start|end/g,(t=>a[t]))}const w=["left","right"],x=["right","left"],v=["top","bottom"],b=["bottom","top"];function A(t,e,n,i){const r=f(t);let o=function(t,e,n){switch(t){case"top":case"bottom":return n?e?x:w:e?w:x;case"left":case"right":return e?v:b;default:return[]}}(c(t),"start"===n,i);return r&&(o=o.map((t=>t+"-"+r)),e&&(o=o.concat(o.map(y)))),o}function R(t){return t.replace(/left|right|bottom|top/g,(t=>o[t]))}function D(t){return"number"!=typeof t?function(t){return{top:0,right:0,bottom:0,left:0,...t}}(t):{top:t,right:t,bottom:t,left:t}}function P(t){const{x:e,y:n,width:i,height:r}=t;return{width:i,height:r,top:n,left:e,right:e+i,bottom:n+r,x:e,y:n}}function E(t,e,n){let{reference:i,floating:r}=t;const o=d(e),a=p(e),l=u(a),s=c(e),m="y"===o,g=i.x+i.width/2-r.width/2,h=i.y+i.height/2-r.height/2,y=i[l]/2-r[l]/2;let w;switch(s){case"top":w={x:g,y:i.y-r.height};break;case"bottom":w={x:g,y:i.y+i.height};break;case"right":w={x:i.x+i.width,y:h};break;case"left":w={x:i.x-r.width,y:h};break;default:w={x:i.x,y:i.y}}switch(f(e)){case"start":w[a]-=y*(n&&m?-1:1);break;case"end":w[a]+=y*(n&&m?-1:1)}return w}const O=async(t,e,n)=>{const{placement:i="bottom",strategy:r="absolute",middleware:o=[],platform:a}=n,l=o.filter(Boolean),s=await(null==a.isRTL?void 0:a.isRTL(e));let c=await a.getElementRects({reference:t,floating:e,strategy:r}),{x:f,y:m}=E(c,i,s),u=i,g={},d=0;for(let n=0;n<l.length;n++){const{name:o,fn:p}=l[n],{x:h,y:y,data:w,reset:x}=await p({x:f,y:m,initialPlacement:i,placement:u,strategy:r,middlewareData:g,rects:c,platform:a,elements:{reference:t,floating:e}});f=null!=h?h:f,m=null!=y?y:m,g={...g,[o]:{...g[o],...w}},x&&d<=50&&(d++,"object"==typeof x&&(x.placement&&(u=x.placement),x.rects&&(c=!0===x.rects?await a.getElementRects({reference:t,floating:e,strategy:r}):x.rects),({x:f,y:m}=E(c,u,s))),n=-1)}return{x:f,y:m,placement:u,strategy:r,middlewareData:g}};async function T(t,e){var n;void 0===e&&(e={});const{x:i,y:r,platform:o,rects:a,elements:l,strategy:c}=t,{boundary:f="clippingAncestors",rootBoundary:m="viewport",elementContext:u="floating",altBoundary:g=!1,padding:d=0}=s(e,t),p=D(d),h=l[g?"floating"===u?"reference":"floating":u],y=P(await o.getClippingRect({element:null==(n=await(null==o.isElement?void 0:o.isElement(h)))||n?h:h.contextElement||await(null==o.getDocumentElement?void 0:o.getDocumentElement(l.floating)),boundary:f,rootBoundary:m,strategy:c})),w="floating"===u?{x:i,y:r,width:a.floating.width,height:a.floating.height}:a.reference,x=await(null==o.getOffsetParent?void 0:o.getOffsetParent(l.floating)),v=await(null==o.isElement?void 0:o.isElement(x))&&await(null==o.getScale?void 0:o.getScale(x))||{x:1,y:1},b=P(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:w,offsetParent:x,strategy:c}):w);return{top:(y.top-b.top+p.top)/v.y,bottom:(b.bottom-y.bottom+p.bottom)/v.y,left:(y.left-b.left+p.left)/v.x,right:(b.right-y.right+p.right)/v.x}}const L=t=>({name:"arrow",options:t,async fn(e){const{x:n,y:r,placement:o,rects:a,platform:c,elements:m,middlewareData:g}=e,{element:d,padding:h=0}=s(t,e)||{};if(null==d)return{};const y=D(h),w={x:n,y:r},x=p(o),v=u(x),b=await c.getDimensions(d),A="y"===x,R=A?"top":"left",P=A?"bottom":"right",E=A?"clientHeight":"clientWidth",O=a.reference[v]+a.reference[x]-w[x]-a.floating[v],T=w[x]-a.reference[x],L=await(null==c.getOffsetParent?void 0:c.getOffsetParent(d));let k=L?L[E]:0;k&&await(null==c.isElement?void 0:c.isElement(L))||(k=m.floating[E]||a.floating[v]);const B=O/2-T/2,C=k/2-b[v]/2-1,H=i(y[R],C),S=i(y[P],C),F=H,M=k-b[v]-S,V=k/2-b[v]/2+B,W=l(F,V,M),j=!g.arrow&&null!=f(o)&&V!==W&&a.reference[v]/2-(V<F?H:S)-b[v]/2<0,z=j?V<F?V-F:V-M:0;return{[x]:w[x]+z,data:{[x]:W,centerOffset:V-W-z,...j&&{alignmentOffset:z}},reset:j}}});const k=function(t){return void 0===t&&(t={}),{name:"autoPlacement",options:t,async fn(e){var i,r,o;const{rects:a,middlewareData:l,placement:m,platform:u,elements:g}=e,{crossAxis:d=!1,alignment:p,allowedPlacements:w=n,autoAlignment:x=!0,...v}=s(t,e),b=void 0!==p||w===n?function(t,e,n){return(t?[...n.filter((e=>f(e)===t)),...n.filter((e=>f(e)!==t))]:n.filter((t=>c(t)===t))).filter((n=>!t||f(n)===t||!!e&&y(n)!==n))}(p||null,x,w):w,A=await T(e,v),R=(null==(i=l.autoPlacement)?void 0:i.index)||0,D=b[R];if(null==D)return{};const P=h(D,a,await(null==u.isRTL?void 0:u.isRTL(g.floating)));if(m!==D)return{reset:{placement:b[0]}};const E=[A[c(D)],A[P[0]],A[P[1]]],O=[...(null==(r=l.autoPlacement)?void 0:r.overflows)||[],{placement:D,overflows:E}],L=b[R+1];if(L)return{data:{index:R+1,overflows:O},reset:{placement:L}};const k=O.map((t=>{const e=f(t.placement);return[t.placement,e&&d?t.overflows.slice(0,2).reduce(((t,e)=>t+e),0):t.overflows[0],t.overflows]})).sort(((t,e)=>t[1]-e[1])),B=(null==(o=k.filter((t=>t[2].slice(0,f(t[0])?2:3).every((t=>t<=0))))[0])?void 0:o[0])||k[0][0];return B!==m?{data:{index:R+1,overflows:O},reset:{placement:B}}:{}}}},B=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,i;const{placement:r,middlewareData:o,rects:a,initialPlacement:l,platform:f,elements:m}=e,{mainAxis:u=!0,crossAxis:g=!0,fallbackPlacements:p,fallbackStrategy:w="bestFit",fallbackAxisSideDirection:x="none",flipAlignment:v=!0,...b}=s(t,e);if(null!=(n=o.arrow)&&n.alignmentOffset)return{};const D=c(r),P=d(l),E=c(l)===l,O=await(null==f.isRTL?void 0:f.isRTL(m.floating)),L=p||(E||!v?[R(l)]:function(t){const e=R(t);return[y(t),e,y(e)]}(l)),k="none"!==x;!p&&k&&L.push(...A(l,v,x,O));const B=[l,...L],C=await T(e,b),H=[];let S=(null==(i=o.flip)?void 0:i.overflows)||[];if(u&&H.push(C[D]),g){const t=h(r,a,O);H.push(C[t[0]],C[t[1]])}if(S=[...S,{placement:r,overflows:H}],!H.every((t=>t<=0))){var F,M;const t=((null==(F=o.flip)?void 0:F.index)||0)+1,e=B[t];if(e){if(!("alignment"===g&&P!==d(e))||S.every((t=>d(t.placement)!==P||t.overflows[0]>0)))return{data:{index:t,overflows:S},reset:{placement:e}}}let n=null==(M=S.filter((t=>t.overflows[0]<=0)).sort(((t,e)=>t.overflows[1]-e.overflows[1]))[0])?void 0:M.placement;if(!n)switch(w){case"bestFit":{var V;const t=null==(V=S.filter((t=>{if(k){const e=d(t.placement);return e===P||"y"===e}return!0})).map((t=>[t.placement,t.overflows.filter((t=>t>0)).reduce(((t,e)=>t+e),0)])).sort(((t,e)=>t[1]-e[1]))[0])?void 0:V[0];t&&(n=t);break}case"initialPlacement":n=l}if(r!==n)return{reset:{placement:n}}}return{}}}};function C(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function H(e){return t.some((t=>e[t]>=0))}const S=function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){const{rects:n}=e,{strategy:i="referenceHidden",...r}=s(t,e);switch(i){case"referenceHidden":{const t=C(await T(e,{...r,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:H(t)}}}case"escaped":{const t=C(await T(e,{...r,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:H(t)}}}default:return{}}}}};function F(t){const e=i(...t.map((t=>t.left))),n=i(...t.map((t=>t.top)));return{x:e,y:n,width:r(...t.map((t=>t.right)))-e,height:r(...t.map((t=>t.bottom)))-n}}const M=function(t){return void 0===t&&(t={}),{name:"inline",options:t,async fn(e){const{placement:n,elements:o,rects:a,platform:l,strategy:f}=e,{padding:m=2,x:u,y:g}=s(t,e),p=Array.from(await(null==l.getClientRects?void 0:l.getClientRects(o.reference))||[]),h=function(t){const e=t.slice().sort(((t,e)=>t.y-e.y)),n=[];let i=null;for(let t=0;t<e.length;t++){const r=e[t];!i||r.y-i.y>i.height/2?n.push([r]):n[n.length-1].push(r),i=r}return n.map((t=>P(F(t))))}(p),y=P(F(p)),w=D(m);const x=await l.getElementRects({reference:{getBoundingClientRect:function(){if(2===h.length&&h[0].left>h[1].right&&null!=u&&null!=g)return h.find((t=>u>t.left-w.left&&u<t.right+w.right&&g>t.top-w.top&&g<t.bottom+w.bottom))||y;if(h.length>=2){if("y"===d(n)){const t=h[0],e=h[h.length-1],i="top"===c(n),r=t.top,o=e.bottom,a=i?t.left:e.left,l=i?t.right:e.right;return{top:r,bottom:o,left:a,right:l,width:l-a,height:o-r,x:a,y:r}}const t="left"===c(n),e=r(...h.map((t=>t.right))),o=i(...h.map((t=>t.left))),a=h.filter((n=>t?n.left===o:n.right===e)),l=a[0].top,s=a[a.length-1].bottom;return{top:l,bottom:s,left:o,right:e,width:e-o,height:s-l,x:o,y:l}}return y}},floating:o.floating,strategy:f});return a.reference.x!==x.reference.x||a.reference.y!==x.reference.y||a.reference.width!==x.reference.width||a.reference.height!==x.reference.height?{reset:{rects:x}}:{}}}},V=new Set(["left","top"]);const W=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,i;const{x:r,y:o,placement:a,middlewareData:l}=e,m=await async function(t,e){const{placement:n,platform:i,elements:r}=t,o=await(null==i.isRTL?void 0:i.isRTL(r.floating)),a=c(n),l=f(n),m="y"===d(n),u=V.has(a)?-1:1,g=o&&m?-1:1,p=s(e,t);let{mainAxis:h,crossAxis:y,alignmentAxis:w}="number"==typeof p?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return l&&"number"==typeof w&&(y="end"===l?-1*w:w),m?{x:y*g,y:h*u}:{x:h*u,y:y*g}}(e,t);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(i=l.arrow)&&i.alignmentOffset?{}:{x:r+m.x,y:o+m.y,data:{...m,placement:a}}}}},j=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:i,placement:r}=e,{mainAxis:o=!0,crossAxis:a=!1,limiter:f={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...u}=s(t,e),g={x:n,y:i},p=await T(e,u),h=d(c(r)),y=m(h);let w=g[y],x=g[h];if(o){const t="y"===y?"bottom":"right";w=l(w+p["y"===y?"top":"left"],w,w-p[t])}if(a){const t="y"===h?"bottom":"right";x=l(x+p["y"===h?"top":"left"],x,x-p[t])}const v=f.fn({...e,[y]:w,[h]:x});return{...v,data:{x:v.x-n,y:v.y-i,enabled:{[y]:o,[h]:a}}}}}},z=function(t){return void 0===t&&(t={}),{options:t,fn(e){const{x:n,y:i,placement:r,rects:o,middlewareData:a}=e,{offset:l=0,mainAxis:f=!0,crossAxis:u=!0}=s(t,e),g={x:n,y:i},p=d(r),h=m(p);let y=g[h],w=g[p];const x=s(l,e),v="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(f){const t="y"===h?"height":"width",e=o.reference[h]-o.floating[t]+v.mainAxis,n=o.reference[h]+o.reference[t]-v.mainAxis;y<e?y=e:y>n&&(y=n)}if(u){var b,A;const t="y"===h?"width":"height",e=V.has(c(r)),n=o.reference[p]-o.floating[t]+(e&&(null==(b=a.offset)?void 0:b[p])||0)+(e?0:v.crossAxis),i=o.reference[p]+o.reference[t]+(e?0:(null==(A=a.offset)?void 0:A[p])||0)-(e?v.crossAxis:0);w<n?w=n:w>i&&(w=i)}return{[h]:y,[p]:w}}}},q=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var n,o;const{placement:a,rects:l,platform:m,elements:u}=e,{apply:g=()=>{},...p}=s(t,e),h=await T(e,p),y=c(a),w=f(a),x="y"===d(a),{width:v,height:b}=l.floating;let A,R;"top"===y||"bottom"===y?(A=y,R=w===(await(null==m.isRTL?void 0:m.isRTL(u.floating))?"start":"end")?"left":"right"):(R=y,A="end"===w?"top":"bottom");const D=b-h.top-h.bottom,P=v-h.left-h.right,E=i(b-h[A],D),O=i(v-h[R],P),L=!e.middlewareData.shift;let k=E,B=O;if(null!=(n=e.middlewareData.shift)&&n.enabled.x&&(B=P),null!=(o=e.middlewareData.shift)&&o.enabled.y&&(k=D),L&&!w){const t=r(h.left,0),e=r(h.right,0),n=r(h.top,0),i=r(h.bottom,0);x?B=v-2*(0!==t||0!==e?t+e:r(h.left,h.right)):k=b-2*(0!==n||0!==i?n+i:r(h.top,h.bottom))}await g({...e,availableWidth:B,availableHeight:k});const C=await m.getDimensions(u.floating);return v!==C.width||b!==C.height?{reset:{rects:!0}}:{}}}};export{L as arrow,k as autoPlacement,O as computePosition,T as detectOverflow,B as flip,S as hide,M as inline,z as limitShift,W as offset,P as rectToClientRect,j as shift,q as size};
