<template>
  <div class="component-library">
    <div class="library-header">
      <h3>组件库</h3>
    </div>
    
    <div class="library-content">
      <!-- 基础图形 -->
      <div class="component-category">
        <div class="category-title">
          <el-icon><Grid /></el-icon>
          <span>基础图形</span>
        </div>
        <div class="component-grid">
          <div
            v-for="component in basicShapes"
            :key="component.type"
            class="component-item"
            :draggable="true"
            @dragstart="handleDragStart($event, component)"
            @click="handleComponentClick(component)"
          >
            <div class="component-icon">
              <el-icon><component :is="component.icon" /></el-icon>
            </div>
            <span class="component-name">{{ component.name }}</span>
          </div>
        </div>
      </div>

      <!-- 文本组件 -->
      <div class="component-category">
        <div class="category-title">
          <el-icon><EditPen /></el-icon>
          <span>文本组件</span>
        </div>
        <div class="component-grid">
          <div
            v-for="component in textComponents"
            :key="component.type"
            class="component-item"
            :draggable="true"
            @dragstart="handleDragStart($event, component)"
            @click="handleComponentClick(component)"
          >
            <div class="component-icon">
              <el-icon><component :is="component.icon" /></el-icon>
            </div>
            <span class="component-name">{{ component.name }}</span>
          </div>
        </div>
      </div>

      <!-- 媒体组件 -->
      <div class="component-category">
        <div class="category-title">
          <el-icon><Picture /></el-icon>
          <span>媒体组件</span>
        </div>
        <div class="component-grid">
          <div
            v-for="component in mediaComponents"
            :key="component.type"
            class="component-item"
            :draggable="true"
            @dragstart="handleDragStart($event, component)"
            @click="handleComponentClick(component)"
          >
            <div class="component-icon">
              <el-icon><component :is="component.icon" /></el-icon>
            </div>
            <span class="component-name">{{ component.name }}</span>
          </div>
        </div>
      </div>

      <!-- 表格组件 -->
      <div class="component-category">
        <div class="category-title">
          <el-icon><Grid /></el-icon>
          <span>表格组件</span>
        </div>
        <div class="component-grid">
          <div
            v-for="component in tableComponents"
            :key="component.type"
            class="component-item"
            :draggable="true"
            @dragstart="handleDragStart($event, component)"
            @click="handleComponentClick(component)"
          >
            <div class="component-icon">
              <el-icon><component :is="component.icon" /></el-icon>
            </div>
            <span class="component-name">{{ component.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import {
  Grid,
  EditPen,
  Picture,
  Document,
  Minus,
  CirclePlus
} from '@element-plus/icons-vue'

import type { ComponentLibraryItem, ComponentType } from '../types'

defineOptions({
  name: "ComponentLibrary"
})

// 事件定义
const emit = defineEmits<{
  'add-component': [type: ComponentType, position: { x: number, y: number }]
}>()

// 基础图形组件
const basicShapes = ref<ComponentLibraryItem[]>([
  {
    type: 'rectangle',
    name: '矩形',
    icon: 'Grid',
    description: '基础矩形图形',
    defaultProps: {
      id: '',
      type: 'rectangle',
      left: 0,
      top: 0,
      width: 100,
      height: 60,
      angle: 0,
      scaleX: 1,
      scaleY: 1,
      fill: '#409EFF',
      stroke: '#409EFF',
      strokeWidth: 1,
      opacity: 1,
      visible: true,
      selectable: true
    }
  },
  {
    type: 'circle',
    name: '圆形',
    icon: 'CirclePlus',
    description: '基础圆形图形',
    defaultProps: {
      id: '',
      type: 'circle',
      left: 0,
      top: 0,
      width: 80,
      height: 80,
      angle: 0,
      scaleX: 1,
      scaleY: 1,
      fill: '#67C23A',
      stroke: '#67C23A',
      strokeWidth: 1,
      opacity: 1,
      visible: true,
      selectable: true
    }
  },
  {
    type: 'line',
    name: '直线',
    icon: 'Minus',
    description: '基础直线',
    defaultProps: {
      id: '',
      type: 'line',
      left: 0,
      top: 0,
      width: 100,
      height: 0,
      angle: 0,
      scaleX: 1,
      scaleY: 1,
      stroke: '#606266',
      strokeWidth: 2,
      opacity: 1,
      visible: true,
      selectable: true
    }
  }
])

// 文本组件
const textComponents = ref<ComponentLibraryItem[]>([
  {
    type: 'text',
    name: '文本',
    icon: 'EditPen',
    description: '单行文本',
    defaultProps: {
      id: '',
      type: 'text',
      left: 0,
      top: 0,
      width: 100,
      height: 30,
      angle: 0,
      scaleX: 1,
      scaleY: 1,
      fill: '#303133',
      opacity: 1,
      visible: true,
      selectable: true,
      customProperties: {
        text: '文本内容',
        fontSize: 16,
        fontFamily: 'Arial',
        fontWeight: 'normal'
      }
    }
  },
  {
    type: 'textbox',
    name: '文本框',
    icon: 'Document',
    description: '多行文本框',
    defaultProps: {
      id: '',
      type: 'textbox',
      left: 0,
      top: 0,
      width: 200,
      height: 100,
      angle: 0,
      scaleX: 1,
      scaleY: 1,
      fill: '#303133',
      opacity: 1,
      visible: true,
      selectable: true,
      customProperties: {
        text: '多行文本内容\n可以换行',
        fontSize: 14,
        fontFamily: 'Arial',
        fontWeight: 'normal'
      }
    }
  }
])

// 媒体组件
const mediaComponents = ref<ComponentLibraryItem[]>([
  {
    type: 'image',
    name: '图片',
    icon: 'Picture',
    description: '图片组件',
    defaultProps: {
      id: '',
      type: 'image',
      left: 0,
      top: 0,
      width: 150,
      height: 100,
      angle: 0,
      scaleX: 1,
      scaleY: 1,
      opacity: 1,
      visible: true,
      selectable: true,
      customProperties: {
        src: '',
        alt: '图片'
      }
    }
  }
])

// 表格组件
const tableComponents = ref<ComponentLibraryItem[]>([
  {
    type: 'table',
    name: '表格',
    icon: 'Grid',
    description: '数据表格',
    defaultProps: {
      id: '',
      type: 'table',
      left: 0,
      top: 0,
      width: 300,
      height: 150,
      angle: 0,
      scaleX: 1,
      scaleY: 1,
      opacity: 1,
      visible: true,
      selectable: true,
      customProperties: {
        rows: 3,
        cols: 3,
        borderWidth: 1,
        borderColor: '#DCDFE6'
      }
    }
  }
])

// 拖拽开始
const handleDragStart = (event: DragEvent, component: ComponentLibraryItem) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/json', JSON.stringify({
      type: component.type,
      defaultProps: component.defaultProps
    }))
    event.dataTransfer.effectAllowed = 'copy'
  }
}

// 点击添加组件
const handleComponentClick = (component: ComponentLibraryItem) => {
  // 在画布中心添加组件
  const centerPosition = { x: 400, y: 300 }
  emit('add-component', component.type, centerPosition)
}
</script>

<style lang="scss" scoped>
.component-library {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.library-header {
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color);

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.library-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.component-category {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.category-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.component-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.component-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--el-bg-color);

  &:hover {
    border-color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
  }
}

.component-icon {
  margin-bottom: 6px;
  font-size: 20px;
  color: var(--el-color-primary);
}

.component-name {
  font-size: 12px;
  color: var(--el-text-color-regular);
  text-align: center;
  line-height: 1.2;
}
</style>
