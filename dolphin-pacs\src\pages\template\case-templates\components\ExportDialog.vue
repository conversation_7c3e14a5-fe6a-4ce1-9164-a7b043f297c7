<template>
  <el-dialog
    v-model="visible"
    title="导出模板"
    width="600px"
    :before-close="handleClose"
  >
    <div class="export-form">
      <el-form :model="exportOptions" label-width="100px">
        <!-- 导出格式 -->
        <el-form-item label="导出格式">
          <el-radio-group v-model="exportOptions.format">
            <el-radio value="pdf">PDF文档</el-radio>
            <el-radio value="png">PNG图片</el-radio>
            <el-radio value="jpg">JPG图片</el-radio>
            <el-radio value="json">JSON模板</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <!-- 导出内容 -->
        <el-form-item label="导出内容">
          <el-checkbox-group v-model="exportOptions.content">
            <el-checkbox value="canvas">画布内容</el-checkbox>
            <el-checkbox value="richtext">富文本内容</el-checkbox>
            <el-checkbox value="variables">模板变量</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <!-- 图片质量 (仅图片格式) -->
        <el-form-item 
          label="图片质量" 
          v-if="['png', 'jpg'].includes(exportOptions.format)"
        >
          <el-slider
            v-model="exportOptions.quality"
            :min="0.1"
            :max="1"
            :step="0.1"
            :format-tooltip="(val) => Math.round(val * 100) + '%'"
          />
        </el-form-item>
        
        <!-- 缩放比例 -->
        <el-form-item label="缩放比例">
          <el-slider
            v-model="exportOptions.scale"
            :min="0.5"
            :max="3"
            :step="0.1"
            :format-tooltip="(val) => val + 'x'"
          />
        </el-form-item>
        
        <!-- 文件名 -->
        <el-form-item label="文件名">
          <el-input
            v-model="exportOptions.filename"
            placeholder="请输入文件名"
          />
        </el-form-item>
        
        <!-- 模板变量数据 (仅JSON格式) -->
        <el-form-item 
          label="示例数据" 
          v-if="exportOptions.format === 'json'"
        >
          <el-switch
            v-model="exportOptions.includeSampleData"
            active-text="包含示例数据"
            inactive-text="仅模板结构"
          />
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 导出预览 -->
    <div class="export-preview" v-if="showPreview">
      <div class="preview-title">导出预览</div>
      <div class="preview-content" ref="previewRef">
        <!-- 这里显示导出内容的预览 -->
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="generatePreview" v-if="!showPreview">生成预览</el-button>
        <el-button type="primary" @click="exportTemplate" :loading="exporting">
          {{ exporting ? '导出中...' : '导出' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import html2canvas from 'html2canvas'
import { jsPDF } from 'jspdf'

import type { TemplateData, ExportOptions } from '../types'

defineOptions({
  name: "ExportDialog"
})

// Props
interface Props {
  modelValue: boolean
  templateData: TemplateData | null
}

const props = defineProps<Props>()

// 事件定义
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'template-exported': [options: ExportOptions]
}>()

// 响应式数据
const exporting = ref(false)
const showPreview = ref(false)
const previewRef = ref<HTMLDivElement>()

const exportOptions = reactive({
  format: 'pdf' as 'pdf' | 'png' | 'jpg' | 'json',
  content: ['canvas', 'richtext'] as string[],
  quality: 0.9,
  scale: 1,
  filename: '',
  includeSampleData: false
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 生成预览
const generatePreview = () => {
  showPreview.value = true
  // TODO: 实现预览生成逻辑
}

// 导出模板
const exportTemplate = async () => {
  if (!props.templateData) {
    ElMessage.error('没有可导出的模板数据')
    return
  }
  
  exporting.value = true
  
  try {
    switch (exportOptions.format) {
      case 'pdf':
        await exportToPDF()
        break
      case 'png':
        await exportToImage('png')
        break
      case 'jpg':
        await exportToImage('jpg')
        break
      case 'json':
        await exportToJSON()
        break
    }
    
    ElMessage.success('导出成功')
    emit('template-exported', exportOptions as ExportOptions)
    handleClose()
    
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

// 导出为PDF
const exportToPDF = async () => {
  const pdf = new jsPDF('p', 'mm', 'a4')
  const pageWidth = pdf.internal.pageSize.getWidth()
  const pageHeight = pdf.internal.pageSize.getHeight()
  
  let yOffset = 10
  
  // 导出画布内容
  if (exportOptions.content.includes('canvas') && props.templateData?.canvas) {
    // TODO: 渲染画布到PDF
    yOffset += 100
  }
  
  // 导出富文本内容
  if (exportOptions.content.includes('richtext') && props.templateData?.content?.html) {
    // 创建临时元素来渲染HTML
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = props.templateData.content.html
    tempDiv.style.width = '190mm'
    tempDiv.style.padding = '20px'
    tempDiv.style.background = 'white'
    document.body.appendChild(tempDiv)
    
    try {
      const canvas = await html2canvas(tempDiv, {
        scale: exportOptions.scale,
        useCORS: true
      })
      
      const imgData = canvas.toDataURL('image/png')
      const imgWidth = pageWidth - 20
      const imgHeight = (canvas.height * imgWidth) / canvas.width
      
      pdf.addImage(imgData, 'PNG', 10, yOffset, imgWidth, imgHeight)
    } finally {
      document.body.removeChild(tempDiv)
    }
  }
  
  const filename = exportOptions.filename || props.templateData.name || '模板'
  pdf.save(`${filename}.pdf`)
}

// 导出为图片
const exportToImage = async (format: 'png' | 'jpg') => {
  // 创建临时容器
  const container = document.createElement('div')
  container.style.width = '800px'
  container.style.background = 'white'
  container.style.padding = '20px'
  
  // 添加画布内容
  if (exportOptions.content.includes('canvas')) {
    // TODO: 添加画布内容
  }
  
  // 添加富文本内容
  if (exportOptions.content.includes('richtext') && props.templateData?.content?.html) {
    const contentDiv = document.createElement('div')
    contentDiv.innerHTML = props.templateData.content.html
    container.appendChild(contentDiv)
  }
  
  document.body.appendChild(container)
  
  try {
    const canvas = await html2canvas(container, {
      scale: exportOptions.scale,
      useCORS: true
    })
    
    const link = document.createElement('a')
    link.download = `${exportOptions.filename || props.templateData?.name || '模板'}.${format}`
    link.href = canvas.toDataURL(`image/${format}`, exportOptions.quality)
    link.click()
  } finally {
    document.body.removeChild(container)
  }
}

// 导出为JSON
const exportToJSON = () => {
  const exportData = {
    template: props.templateData,
    exportOptions: exportOptions,
    exportTime: new Date().toISOString()
  }
  
  if (exportOptions.includeSampleData) {
    exportData.sampleData = {
      patientName: '张三',
      patientAge: '35',
      examDate: '2024-01-15',
      doctorName: '李医生'
    }
  }
  
  const dataStr = JSON.stringify(exportData, null, 2)
  const blob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  
  const link = document.createElement('a')
  link.download = `${exportOptions.filename || props.templateData?.name || '模板'}.json`
  link.href = url
  link.click()
  
  URL.revokeObjectURL(url)
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  showPreview.value = false
  
  // 重置表单
  exportOptions.format = 'pdf'
  exportOptions.content = ['canvas', 'richtext']
  exportOptions.quality = 0.9
  exportOptions.scale = 1
  exportOptions.filename = ''
  exportOptions.includeSampleData = false
}
</script>

<style lang="scss" scoped>
.export-form {
  .el-form-item {
    margin-bottom: 20px;
  }
}

.export-preview {
  margin-top: 20px;
  border-top: 1px solid var(--el-border-color);
  padding-top: 20px;
  
  .preview-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--el-text-color-primary);
  }
  
  .preview-content {
    height: 200px;
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--el-text-color-placeholder);
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
