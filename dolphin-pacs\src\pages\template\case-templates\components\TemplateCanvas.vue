<template>
  <div class="template-canvas">
    <div class="canvas-container" ref="canvasContainer">
      <canvas ref="canvasRef" />
      
      <!-- 加载遮罩 -->
      <div v-if="isLoading" class="loading-overlay">
        <el-loading-spinner />
        <span>加载中...</span>
      </div>
    </div>
    
    <!-- 画布工具栏 -->
    <div class="canvas-toolbar">
      <el-button-group size="small">
        <el-button @click="selectMode" :type="mode === 'select' ? 'primary' : 'default'">
          <el-icon><Pointer /></el-icon>
          选择
        </el-button>
        <el-button @click="panMode" :type="mode === 'pan' ? 'primary' : 'default'">
          <el-icon><Rank /></el-icon>
          拖拽
        </el-button>
      </el-button-group>
      
      <el-divider direction="vertical" />
      
      <el-button-group size="small">
        <el-button @click="bringToFront" :disabled="!selectedObject">
          <el-icon><Top /></el-icon>
          置顶
        </el-button>
        <el-button @click="sendToBack" :disabled="!selectedObject">
          <el-icon><Bottom /></el-icon>
          置底
        </el-button>
      </el-button-group>
      
      <el-divider direction="vertical" />
      
      <el-button-group size="small">
        <el-button @click="deleteSelected" :disabled="!selectedObject">
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
        <el-button @click="duplicateSelected" :disabled="!selectedObject">
          <el-icon><CopyDocument /></el-icon>
          复制
        </el-button>
      </el-button-group>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, nextTick, watch, markRaw } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Pointer,
  Rank,
  Top,
  Bottom,
  Delete,
  CopyDocument
} from '@element-plus/icons-vue'
import * as fabric from 'fabric'

import type { CanvasObject, ComponentType } from '../types'

defineOptions({
  name: "TemplateCanvas"
})

// Props
interface Props {
  zoomLevel: number
}

const props = withDefaults(defineProps<Props>(), {
  zoomLevel: 1
})

// 事件定义
const emit = defineEmits<{
  'object-selected': [object: CanvasObject | null]
  'object-modified': [object: CanvasObject]
  'canvas-updated': []
}>()

// 响应式数据
const canvasRef = ref<HTMLCanvasElement>()
const canvasContainer = ref<HTMLDivElement>()
const isLoading = ref(false)
const mode = ref<'select' | 'pan'>('select')
const selectedObject = ref<CanvasObject | null>(null)

// Fabric.js 实例
let fabricCanvas: fabric.Canvas | null = null
const canvasObjects = ref<CanvasObject[]>([])
const history = ref<string[]>([])
const historyIndex = ref(-1)

// 初始化画布
const initCanvas = async () => {
  if (!canvasRef.value || !canvasContainer.value) return
  
  try {
    isLoading.value = true
    
    // 等待容器尺寸就绪
    await nextTick()
    
    const containerRect = canvasContainer.value.getBoundingClientRect()
    const canvasWidth = Math.max(containerRect.width - 40, 800)
    const canvasHeight = Math.max(containerRect.height - 80, 600)
    
    // 创建 Fabric.js 画布
    fabricCanvas = markRaw(new fabric.Canvas(canvasRef.value, {
      width: canvasWidth,
      height: canvasHeight,
      backgroundColor: '#ffffff',
      selection: true,
      preserveObjectStacking: true
    }))
    
    // 添加事件监听
    setupEventListeners()
    
    // 启用拖拽接收
    setupDropZone()
    
    isLoading.value = false
    
  } catch (error) {
    console.error('画布初始化失败:', error)
    ElMessage.error('画布初始化失败')
    isLoading.value = false
  }
}

// 设置事件监听
const setupEventListeners = () => {
  if (!fabricCanvas) return
  
  // 对象选择事件
  fabricCanvas.on('selection:created', handleSelectionCreated)
  fabricCanvas.on('selection:updated', handleSelectionUpdated)
  fabricCanvas.on('selection:cleared', handleSelectionCleared)
  
  // 对象修改事件
  fabricCanvas.on('object:modified', handleObjectModified)
  fabricCanvas.on('object:moving', handleObjectMoving)
  fabricCanvas.on('object:scaling', handleObjectScaling)
  fabricCanvas.on('object:rotating', handleObjectRotating)
  
  // 画布事件
  fabricCanvas.on('path:created', handleCanvasUpdated)
  fabricCanvas.on('object:added', handleCanvasUpdated)
  fabricCanvas.on('object:removed', handleCanvasUpdated)
}

// 设置拖拽区域
const setupDropZone = () => {
  if (!canvasContainer.value) return
  
  canvasContainer.value.addEventListener('dragover', handleDragOver)
  canvasContainer.value.addEventListener('drop', handleDrop)
}

// 拖拽悬停
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  event.dataTransfer!.dropEffect = 'copy'
}

// 拖拽放置
const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  
  try {
    const data = JSON.parse(event.dataTransfer!.getData('application/json'))
    const rect = canvasContainer.value!.getBoundingClientRect()
    const x = event.clientX - rect.left - 20
    const y = event.clientY - rect.top - 60
    
    addComponent(data.type, { x, y }, data.defaultProps)
  } catch (error) {
    console.error('拖拽添加组件失败:', error)
  }
}

// 添加组件
const addComponent = (type: ComponentType, position: { x: number, y: number }, defaultProps?: any) => {
  if (!fabricCanvas) return
  
  const id = `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  
  let fabricObject: fabric.Object | null = null
  
  switch (type) {
    case 'rectangle':
      fabricObject = new fabric.Rect({
        left: position.x,
        top: position.y,
        width: defaultProps?.width || 100,
        height: defaultProps?.height || 60,
        fill: defaultProps?.fill || '#409EFF',
        stroke: defaultProps?.stroke || '#409EFF',
        strokeWidth: defaultProps?.strokeWidth || 1
      })
      break
      
    case 'circle':
      fabricObject = new fabric.Circle({
        left: position.x,
        top: position.y,
        radius: (defaultProps?.width || 80) / 2,
        fill: defaultProps?.fill || '#67C23A',
        stroke: defaultProps?.stroke || '#67C23A',
        strokeWidth: defaultProps?.strokeWidth || 1
      })
      break
      
    case 'line':
      fabricObject = new fabric.Line([
        position.x, position.y,
        position.x + (defaultProps?.width || 100), position.y
      ], {
        stroke: defaultProps?.stroke || '#606266',
        strokeWidth: defaultProps?.strokeWidth || 2
      })
      break
      
    case 'text':
      fabricObject = new fabric.Text(defaultProps?.customProperties?.text || '文本内容', {
        left: position.x,
        top: position.y,
        fontSize: defaultProps?.customProperties?.fontSize || 16,
        fontFamily: defaultProps?.customProperties?.fontFamily || 'Arial',
        fill: defaultProps?.fill || '#303133'
      })
      break
      
    case 'textbox':
      fabricObject = new fabric.Textbox(defaultProps?.customProperties?.text || '多行文本内容', {
        left: position.x,
        top: position.y,
        width: defaultProps?.width || 200,
        fontSize: defaultProps?.customProperties?.fontSize || 14,
        fontFamily: defaultProps?.customProperties?.fontFamily || 'Arial',
        fill: defaultProps?.fill || '#303133'
      })
      break
      
    case 'image':
      // 图片组件需要特殊处理
      addImagePlaceholder(position, defaultProps)
      return
      
    default:
      ElMessage.warning(`暂不支持 ${type} 类型的组件`)
      return
  }
  
  if (fabricObject) {
    // 设置对象ID
    fabricObject.set('id', id)
    
    // 添加到画布
    fabricCanvas.add(fabricObject)
    fabricCanvas.setActiveObject(fabricObject)
    fabricCanvas.renderAll()
    
    // 保存历史记录
    saveHistory()
    
    ElMessage.success(`已添加${getComponentName(type)}`)
  }
}

// 添加图片占位符
const addImagePlaceholder = (position: { x: number, y: number }, defaultProps?: any) => {
  if (!fabricCanvas) return
  
  const placeholder = new fabric.Rect({
    left: position.x,
    top: position.y,
    width: defaultProps?.width || 150,
    height: defaultProps?.height || 100,
    fill: '#f0f0f0',
    stroke: '#ddd',
    strokeWidth: 2,
    strokeDashArray: [5, 5]
  })
  
  const text = new fabric.Text('点击上传图片', {
    left: position.x + (defaultProps?.width || 150) / 2,
    top: position.y + (defaultProps?.height || 100) / 2,
    fontSize: 12,
    fill: '#999',
    textAlign: 'center',
    originX: 'center',
    originY: 'center'
  })
  
  const group = new fabric.Group([placeholder, text], {
    left: position.x,
    top: position.y
  })
  
  group.set('id', `image_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`)
  
  fabricCanvas.add(group)
  fabricCanvas.setActiveObject(group)
  fabricCanvas.renderAll()
  
  saveHistory()
}

// 获取组件名称
const getComponentName = (type: ComponentType): string => {
  const names: Record<ComponentType, string> = {
    rectangle: '矩形',
    circle: '圆形',
    line: '直线',
    text: '文本',
    textbox: '文本框',
    image: '图片',
    table: '表格'
  }
  return names[type] || type
}

// 事件处理函数
const handleSelectionCreated = (event: fabric.IEvent) => {
  const activeObject = event.selected?.[0]
  if (activeObject) {
    selectedObject.value = fabricObjectToCanvasObject(activeObject)
    emit('object-selected', selectedObject.value)
  }
}

const handleSelectionUpdated = (event: fabric.IEvent) => {
  const activeObject = event.selected?.[0]
  if (activeObject) {
    selectedObject.value = fabricObjectToCanvasObject(activeObject)
    emit('object-selected', selectedObject.value)
  }
}

const handleSelectionCleared = () => {
  selectedObject.value = null
  emit('object-selected', null)
}

const handleObjectModified = (event: fabric.IEvent) => {
  if (event.target) {
    const canvasObj = fabricObjectToCanvasObject(event.target)
    emit('object-modified', canvasObj)
    saveHistory()
  }
}

const handleObjectMoving = (event: fabric.IEvent) => {
  // 可以添加移动时的约束逻辑
}

const handleObjectScaling = (event: fabric.IEvent) => {
  // 可以添加缩放时的约束逻辑
}

const handleObjectRotating = (event: fabric.IEvent) => {
  // 可以添加旋转时的约束逻辑
}

const handleCanvasUpdated = () => {
  emit('canvas-updated')
}

// 转换 Fabric 对象为 Canvas 对象
const fabricObjectToCanvasObject = (fabricObj: fabric.Object): CanvasObject => {
  return {
    id: fabricObj.get('id') || '',
    type: fabricObj.type as ComponentType,
    left: fabricObj.left || 0,
    top: fabricObj.top || 0,
    width: fabricObj.width || 0,
    height: fabricObj.height || 0,
    angle: fabricObj.angle || 0,
    scaleX: fabricObj.scaleX || 1,
    scaleY: fabricObj.scaleY || 1,
    fill: fabricObj.fill as string,
    stroke: fabricObj.stroke as string,
    strokeWidth: fabricObj.strokeWidth || 0,
    opacity: fabricObj.opacity || 1,
    visible: fabricObj.visible !== false,
    selectable: fabricObj.selectable !== false,
    fabricObject: markRaw(fabricObj)
  }
}

// 工具栏操作
const selectMode = () => {
  mode.value = 'select'
  if (fabricCanvas) {
    fabricCanvas.isDrawingMode = false
    fabricCanvas.selection = true
    fabricCanvas.forEachObject(obj => {
      obj.selectable = true
    })
  }
}

const panMode = () => {
  mode.value = 'pan'
  if (fabricCanvas) {
    fabricCanvas.isDrawingMode = false
    fabricCanvas.selection = false
    fabricCanvas.forEachObject(obj => {
      obj.selectable = false
    })
  }
}

const bringToFront = () => {
  const activeObject = fabricCanvas?.getActiveObject()
  if (activeObject) {
    fabricCanvas?.bringToFront(activeObject)
    fabricCanvas?.renderAll()
    saveHistory()
  }
}

const sendToBack = () => {
  const activeObject = fabricCanvas?.getActiveObject()
  if (activeObject) {
    fabricCanvas?.sendToBack(activeObject)
    fabricCanvas?.renderAll()
    saveHistory()
  }
}

const deleteSelected = () => {
  const activeObject = fabricCanvas?.getActiveObject()
  if (activeObject) {
    fabricCanvas?.remove(activeObject)
    fabricCanvas?.renderAll()
    selectedObject.value = null
    emit('object-selected', null)
    saveHistory()
    ElMessage.success('已删除选中对象')
  }
}

const duplicateSelected = () => {
  const activeObject = fabricCanvas?.getActiveObject()
  if (activeObject) {
    activeObject.clone((cloned: fabric.Object) => {
      cloned.set({
        left: (cloned.left || 0) + 10,
        top: (cloned.top || 0) + 10,
        id: `${cloned.type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      })
      fabricCanvas?.add(cloned)
      fabricCanvas?.setActiveObject(cloned)
      fabricCanvas?.renderAll()
      saveHistory()
      ElMessage.success('已复制对象')
    })
  }
}

// 历史记录
const saveHistory = () => {
  if (!fabricCanvas) return

  const state = JSON.stringify(fabricCanvas.toJSON(['id']))

  // 移除当前位置之后的历史记录
  history.value = history.value.slice(0, historyIndex.value + 1)

  // 添加新的历史记录
  history.value.push(state)
  historyIndex.value = history.value.length - 1

  // 限制历史记录数量
  if (history.value.length > 50) {
    history.value.shift()
    historyIndex.value--
  }
}

// 撤销
const undo = () => {
  if (historyIndex.value > 0) {
    historyIndex.value--
    loadFromHistory()
  }
}

// 重做
const redo = () => {
  if (historyIndex.value < history.value.length - 1) {
    historyIndex.value++
    loadFromHistory()
  }
}

// 从历史记录加载
const loadFromHistory = () => {
  if (!fabricCanvas || historyIndex.value < 0) return

  const state = history.value[historyIndex.value]
  fabricCanvas.loadFromJSON(state, () => {
    fabricCanvas?.renderAll()
    selectedObject.value = null
    emit('object-selected', null)
  })
}

// 检查是否可以撤销/重做
const canUndo = () => historyIndex.value > 0
const canRedo = () => historyIndex.value < history.value.length - 1

// 更新对象属性
const updateObjectProperty = (property: string, value: any) => {
  const activeObject = fabricCanvas?.getActiveObject()
  if (activeObject) {
    activeObject.set(property, value)
    fabricCanvas?.renderAll()

    // 更新选中对象信息
    selectedObject.value = fabricObjectToCanvasObject(activeObject)
    emit('object-modified', selectedObject.value)

    saveHistory()
  }
}

// 获取画布对象
const getCanvasObjects = (): CanvasObject[] => {
  if (!fabricCanvas) return []

  return fabricCanvas.getObjects().map(obj => fabricObjectToCanvasObject(obj))
}

// 监听缩放级别变化
watch(() => props.zoomLevel, (newZoom) => {
  if (fabricCanvas) {
    fabricCanvas.setZoom(newZoom)
    fabricCanvas.renderAll()
  }
})

// 暴露方法给父组件
defineExpose({
  addComponent,
  undo,
  redo,
  canUndo,
  canRedo,
  updateObjectProperty,
  getCanvasObjects
})

// 生命周期
onMounted(() => {
  initCanvas()
})

onUnmounted(() => {
  if (fabricCanvas) {
    fabricCanvas.dispose()
  }

  if (canvasContainer.value) {
    canvasContainer.value.removeEventListener('dragover', handleDragOver)
    canvasContainer.value.removeEventListener('drop', handleDrop)
  }
})
</script>

<style lang="scss" scoped>
.template-canvas {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: #f5f5f5;

  canvas {
    border: 1px solid var(--el-border-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin: 20px;
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  z-index: 10;

  span {
    font-size: 14px;
    color: var(--el-text-color-regular);
  }
}

.canvas-toolbar {
  position: absolute;
  top: 20px;
  left: 20px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 5;
}
</style>
