<template>
  <el-dialog
    v-model="visible"
    title="模板预览"
    width="80%"
    :before-close="handleClose"
  >
    <div class="preview-container">
      <!-- 预览工具栏 -->
      <div class="preview-toolbar">
        <div class="toolbar-left">
          <span class="preview-title">{{ templateData?.name }}</span>
        </div>
        
        <div class="toolbar-right">
          <el-button-group size="small">
            <el-button @click="zoomOut" :icon="ZoomOut">缩小</el-button>
            <el-button @click="resetZoom">{{ Math.round(zoomLevel * 100) }}%</el-button>
            <el-button @click="zoomIn" :icon="ZoomIn">放大</el-button>
          </el-button-group>
          
          <el-divider direction="vertical" />
          
          <el-button @click="exportPreview" :icon="Download" type="primary">
            导出预览
          </el-button>
        </div>
      </div>
      
      <!-- 预览内容 -->
      <div class="preview-content" ref="previewContentRef">
        <div class="preview-wrapper" :style="{ transform: `scale(${zoomLevel})` }">
          <!-- 画布预览 -->
          <div class="canvas-preview" v-if="templateData?.canvas">
            <canvas ref="previewCanvasRef" />
          </div>
          
          <!-- 富文本内容预览 -->
          <div class="content-preview" v-if="templateData?.content?.html">
            <div class="content-wrapper" v-html="processedContent" />
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { ZoomOut, ZoomIn, Download } from '@element-plus/icons-vue'
import * as fabric from 'fabric'
import html2canvas from 'html2canvas'
import { jsPDF } from 'jspdf'
import template from 'lodash.template'

import type { TemplateData } from '../types'

defineOptions({
  name: "PreviewDialog"
})

// Props
interface Props {
  modelValue: boolean
  templateData: TemplateData | null
}

const props = defineProps<Props>()

// 事件定义
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const zoomLevel = ref(1)
const previewContentRef = ref<HTMLDivElement>()
const previewCanvasRef = ref<HTMLCanvasElement>()

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 示例数据用于预览
const sampleData = {
  patientName: '张三',
  patientAge: '35',
  patientGender: '男',
  examDate: '2024-01-15',
  doctorName: '李医生',
  hospitalName: '某某医院'
}

// 处理模板内容
const processedContent = computed(() => {
  if (!props.templateData?.content?.html) return ''
  
  try {
    const compiled = template(props.templateData.content.html, {
      interpolate: /\{\{(.+?)\}\}/g
    })
    return compiled(sampleData)
  } catch (error) {
    console.error('模板处理失败:', error)
    return props.templateData.content.html
  }
})

// 缩放操作
const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value * 1.2, 3)
}

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value / 1.2, 0.1)
}

const resetZoom = () => {
  zoomLevel.value = 1
}

// 渲染画布预览
const renderCanvasPreview = async () => {
  if (!props.templateData?.canvas || !previewCanvasRef.value) return
  
  try {
    const canvas = new fabric.Canvas(previewCanvasRef.value, {
      width: props.templateData.canvas.width,
      height: props.templateData.canvas.height,
      backgroundColor: props.templateData.canvas.backgroundColor || '#ffffff'
    })
    
    // 这里需要根据实际的画布对象数据来重建画布
    // 由于这是预览，我们可以简化处理
    canvas.renderAll()
    
  } catch (error) {
    console.error('画布预览渲染失败:', error)
  }
}

// 导出预览
const exportPreview = async () => {
  if (!previewContentRef.value) return
  
  try {
    const canvas = await html2canvas(previewContentRef.value, {
      scale: 2,
      useCORS: true,
      allowTaint: true
    })
    
    const imgData = canvas.toDataURL('image/png')
    
    // 创建PDF
    const pdf = new jsPDF('p', 'mm', 'a4')
    const imgWidth = 190
    const imgHeight = (canvas.height * imgWidth) / canvas.width
    
    pdf.addImage(imgData, 'PNG', 10, 10, imgWidth, imgHeight)
    pdf.save(`${props.templateData?.name || '模板预览'}.pdf`)
    
    ElMessage.success('预览导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 监听对话框打开
watch(visible, async (newVisible) => {
  if (newVisible && props.templateData) {
    await nextTick()
    renderCanvasPreview()
  }
})
</script>

<style lang="scss" scoped>
.preview-container {
  height: 70vh;
  display: flex;
  flex-direction: column;
}

.preview-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid var(--el-border-color);
  margin-bottom: 16px;

  .toolbar-left {
    .preview-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }

  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

.preview-content {
  flex: 1;
  overflow: auto;
  background: #f5f5f5;
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  padding: 20px;
}

.preview-wrapper {
  transform-origin: top left;
  transition: transform 0.2s ease;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  overflow: hidden;
}

.canvas-preview {
  border-bottom: 1px solid var(--el-border-color);
  
  canvas {
    display: block;
    max-width: 100%;
  }
}

.content-preview {
  padding: 20px;
  
  .content-wrapper {
    :deep(h1), :deep(h2), :deep(h3) {
      margin: 16px 0 8px 0;
      font-weight: 600;
    }
    
    :deep(p) {
      margin: 8px 0;
      line-height: 1.6;
    }
    
    :deep(table) {
      width: 100%;
      border-collapse: collapse;
      margin: 16px 0;
      
      td, th {
        border: 1px solid var(--el-border-color);
        padding: 8px 12px;
        text-align: left;
      }
      
      th {
        background: var(--el-bg-color-page);
        font-weight: 600;
      }
    }
    
    :deep(.template-variable) {
      background: var(--el-color-success-light-9);
      color: var(--el-color-success);
      padding: 2px 6px;
      border-radius: 4px;
      font-weight: 500;
    }
  }
}
</style>
