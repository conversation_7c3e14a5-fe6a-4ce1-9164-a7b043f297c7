{"version": 3, "file": "cache-storage.js", "sourceRoot": "", "sources": ["../../../../src/core/__tests__/cache-storage.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iCAA6C;AAC7C,wCAAqC;AACrC,kDAA8C;AAC9C,sCAAmC;AACnC,kDAA+C;AAE/C,IAAM,KAAK,GAAG,0BAA0B,CAAC;AAEzC,IAAM,iBAAiB,GAAG,UAAC,MAAc,EAAE,IAAS;IAAT,qBAAA,EAAA,SAAS;IAChD,IAAM,OAAO,GAAG;QACZ,QAAQ,EAAE;YACN,IAAI,EAAE,MAAM;SACf;QACD,QAAQ,EAAE;YACN,aAAa,EAAb,UAAc,KAAa;gBACvB,IAAI,KAAK,GAAG,EAAE,CAAC;gBACf,OAAO;oBACH,IAAI,IAAI,CAAC,KAAa;wBAClB,KAAK,GAAG,KAAK,CAAC;oBAClB,CAAC;oBACD,IAAI,IAAI;wBACJ,OAAO,KAAK,CAAC;oBACjB,CAAC;oBACD,IAAI,QAAQ;wBACR,OAAO,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;oBACnC,CAAC;oBACD,IAAI,QAAQ;wBACR,OAAO,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;oBACnC,CAAC;oBACD,IAAI,IAAI;wBACJ,OAAO,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;oBAC/B,CAAC;iBACJ,CAAC;YACN,CAAC;SACJ;KACJ,CAAC;IAEF,4BAAY,CAAC,UAAU,CAAC,OAAiB,CAAC,CAAC;IAE3C,OAAO,IAAI,iBAAO,YAEV,OAAO,EAAE,KAAK,EACd,YAAY,EAAE,CAAC,EACf,OAAO,EAAE,KAAK,EACd,UAAU,EAAE,KAAK,EACjB,KAAK,OAAA,IACF,IAAI,GAEX,IAAI,eAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CACzB,CAAC;AACN,CAAC,CAAC;AAEF,IAAM,MAAM,GAAgB,EAAE,CAAC;AAC/B,IAAM,GAAG,GAAyB,EAAE,CAAC;AACrC,IAAM,KAAK,GAAG,UAAO,OAAe;;gBAAK,qBAAM,IAAI,OAAO,CAAC,UAAC,OAAO,IAAK,OAAA,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,EAA5B,CAA4B,CAAC,EAAA;gBAA5D,sBAAA,SAA4D,EAAA;;SAAA,CAAC;AAEtG;IAII;QACI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;IACL,gBAAC;AAAD,CAAC,AAPD,IAOC;AAED;IASI;QACI,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;QAClB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnB,CAAC;IAEK,iCAAI,GAAV,UAAW,MAAc,EAAE,QAAgB;;;;;wBACvC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;wBACzB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;wBACrB,IAAI,IAAI,CAAC,MAAM,EAAE;4BACb,IAAI,CAAC,MAAM,EAAE,CAAC;yBACjB;wBACD,qBAAM,KAAK,CAAC,CAAC,CAAC,EAAA;;wBAAd,SAAc,CAAC;;;;;KAClB;IAED,iCAAI,GAAJ,UAAK,MAAc,EAAE,GAAW;QAC5B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACnB,CAAC;IACD,iCAAI,GAAJ;QACI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACrB,CAAC;IACL,yBAAC;AAAD,CAAC,AAhCD,IAgCC;AAED,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,EAAE,EAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AAC3E,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,gBAAgB,EAAE;IAC5C,KAAK,EAAE,kBAAkB;IACzB,QAAQ,EAAE,IAAI;CACjB,CAAC,CAAC;AAEH,IAAM,WAAW,GAAG,UAAC,IAAmC;IAAnC,qBAAA,EAAA,SAAmC;IACpD,IAAM,QAAQ,GAA6B;QACvC,mBAAmB,EAAE,IAAI;QACzB,mBAAmB,EAAE,IAAI;QACzB,gBAAgB,EAAE,IAAI;QACtB,qBAAqB,EAAE,KAAK;KAC/B,CAAC;IAEF,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;QAC9B,MAAM,CAAC,cAAc,CAAC,mBAAQ,EAAE,GAAG,EAAE;YACjC,KAAK,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;YACjE,QAAQ,EAAE,IAAI;SACjB,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AAEF,QAAQ,CAAC,eAAe,EAAE;IACtB,UAAU,CAAC,cAAM,OAAA,WAAW,EAAE,EAAb,CAAa,CAAC,CAAC;IAChC,SAAS,CAAC;QACN,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QAC1B,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,+BAA+B,EAAE;;;;;oBACzB,KAAK,GAAI,iBAAiB,CAAC,oBAAoB,EAAE,EAAC,KAAK,EAAE,IAAI,EAAC,CAAC,MAA1D,CAA2D;oBACvE,qBAAM,KAAK,CAAC,QAAQ,CAAC,6BAA6B,CAAC,EAAA;;oBAAnD,SAAmD,CAAC;oBACpD,qBAAM,KAAK,CAAC,QAAQ,CAAC,8BAA8B,CAAC,EAAA;;oBAApD,SAAoD,CAAC;oBAErD,wBAAe,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;oBAClC,wBAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,6BAA6B,CAAC,CAAC;oBAC9D,wBAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,8BAA8B,CAAC,CAAC;;;;SAClE,CAAC,CAAC;IAEH,EAAE,CAAC,2CAA2C,EAAE;;;;;oBACrC,KAAK,GAAI,iBAAiB,CAAC,oBAAoB,CAAC,MAA3C,CAA4C;oBACxD,qBAAM,KAAK,CAAC,QAAQ,CAAC,6BAA6B,CAAC,EAAA;;oBAAnD,SAAmD,CAAC;oBACpD,qBAAM,KAAK,CAAC,QAAQ,CAAC,6BAA6B,CAAC,EAAA;;oBAAnD,SAAmD,CAAC;oBAEpD,wBAAe,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;oBAClC,wBAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,6BAA6B,CAAC,CAAC;;;;SACjE,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,EAAE;QACZ,EAAE,CAAC,iCAAiC,EAAE;;;;;wBAC3B,KAAK,GAAI,iBAAiB,CAAC,oBAAoB,CAAC,MAA3C,CAA4C;wBACxD,qBAAM,KAAK,CAAC,QAAQ,CAAC,6BAA6B,CAAC,EAAA;;wBAAnD,SAAmD,CAAC;wBACpD,qBAAM,KAAK,CAAC,QAAQ,CAAC,8BAA8B,CAAC,EAAA;;wBAApD,SAAoD,CAAC;wBAErD,wBAAe,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;wBAClC,wBAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,6BAA6B,CAAC,CAAC;wBAC9D,wBAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,8BAA8B,CAAC,CAAC;;;;aAClE,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE;;;;;wBAC1C,WAAW,CAAC,EAAC,mBAAmB,EAAE,KAAK,EAAC,CAAC,CAAC;wBACnC,KAAK,GAAI,iBAAiB,CAAC,oBAAoB,CAAC,MAA3C,CAA4C;wBACxD,qBAAM,KAAK,CAAC,QAAQ,CAAC,6BAA6B,CAAC,EAAA;;wBAAnD,SAAmD,CAAC;wBACpD,qBAAM,KAAK,CAAC,QAAQ,CAAC,8BAA8B,CAAC,EAAA;;wBAApD,SAAoD,CAAC;wBAErD,wBAAe,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;;;;aACrC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE;QACrB,EAAE,CAAC,sDAAsD,EAAE;;;;;wBAChD,KAAK,GAAI,iBAAiB,CAAC,oBAAoB,EAAE;4BACpD,KAAK,EAAE,SAAS;yBACnB,CAAC,MAFU,CAET;wBACH,qBAAM,KAAK,CAAC,QAAQ,CAAC,yCAAyC,CAAC,EAAA;;wBAA/D,SAA+D,CAAC;wBAChE,wBAAe,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;;;;aACrC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE;;;;;wBAC1C,KAAK,GAAI,iBAAiB,CAAC,oBAAoB,EAAE;4BACpD,UAAU,EAAE,IAAI;4BAChB,KAAK,EAAE,SAAS;yBACnB,CAAC,MAHU,CAGT;wBACH,qBAAM,KAAK,CAAC,QAAQ,CAAC,yCAAyC,CAAC,EAAA;;wBAA/D,SAA+D,CAAC;wBAChE,wBAAe,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;wBAClC,wBAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,yCAAyC,CAAC,CAAC;wBAC1E,wBAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;;;;aACrD,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE;;;;;wBACtC,KAAK,GAAI,iBAAiB,CAAC,oBAAoB,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,MAA5D,CAA6D;wBACzE,qBAAM,KAAK,CAAC,QAAQ,CAAC,yCAAyC,CAAC,EAAA;;wBAA/D,SAA+D,CAAC;wBAChE,wBAAe,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;wBAClC,wBAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,yCAAyC,CAAC,CAAC;wBAC1E,wBAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;;;;aACvD,CAAC,CAAC;QAEH,EAAE,CAAC,kEAAkE,EAAE;;;;;wBACnE,WAAW,CAAC,EAAC,mBAAmB,EAAE,KAAK,EAAC,CAAC,CAAC;wBAEnC,KAAK,GAAI,iBAAiB,CAAC,oBAAoB,EAAE;4BACpD,OAAO,EAAE,IAAI;4BACb,KAAK,EAAE,SAAS;yBACnB,CAAC,MAHU,CAGT;wBACH,qBAAM,KAAK,CAAC,QAAQ,CAAC,yCAAyC,CAAC,EAAA;;wBAA/D,SAA+D,CAAC;wBAChE,wBAAe,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;;;;aACrC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE;;;;;wBACnD,KAAK,GAAI,iBAAiB,CAAC,oBAAoB,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,MAA5D,CAA6D;wBACzE,qBAAM,KAAK,CAAC,QAAQ,CAAC,yCAAyC,CAAC,EAAA;;wBAA/D,SAA+D,CAAC;wBAChE,wBAAe,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;wBAClC,wBAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,yCAAyC,CAAC,CAAC;wBAC1E,wBAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;;;;aACvD,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE;;;;;wBACtB,KAAK,GAAI,iBAAiB,CAAC,oBAAoB,CAAC,MAA3C,CAA4C;wBACxD,qBAAM,KAAK,CAAC,QAAQ,CAAC,yCAAyC,CAAC,EAAA;;wBAA/D,SAA+D,CAAC;wBAChE,wBAAe,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;wBAC/B,wBAAe,CACX,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EACP,KAAK,aAAQ,kBAAkB,CAAC,yCAAyC,CAAC,uBAAoB,CACpG,CAAC;wBACF,qBAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,iBAAiB,CAAC,EAAA;;wBAAzC,SAAyC,CAAC;wBAE1C,wBAAe,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;wBAClC,wBAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;;;;aACrD,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE;;;;;wBAC7B,KAAK,GAAI,iBAAiB,CAAC,oBAAoB,EAAE;4BACpD,YAAY,EAAE,EAAE;yBACnB,CAAC,MAFU,CAET;wBACH,qBAAM,KAAK,CAAC,QAAQ,CAAC,yCAAyC,CAAC,EAAA;;wBAA/D,SAA+D,CAAC;wBAEhE,wBAAe,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;wBAC/B,wBAAe,CACX,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EACP,KAAK,aAAQ,kBAAkB,CAAC,yCAAyC,CAAC,uBAAoB,CACpG,CAAC;wBACF,wBAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;wBACpC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE;4BAClB,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;yBACtB;;;;wBAEG,qBAAM,KAAK,CAAC,KAAK,CAAC,yCAAyC,CAAC,EAAA;;wBAA5D,SAA4D,CAAC;wBAC7D,aAAI,CAAC,4BAA4B,CAAC,CAAC;;;;;;;;aAE1C,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iCAAiC,EAAE;;;;;oBAC3B,KAAK,GAAI,iBAAiB,CAAC,oBAAoB,CAAC,MAA3C,CAA4C;oBACxD,qBAAM,KAAK,CAAC,QAAQ,CAAC,6BAA6B,CAAC,EAAA;;oBAAnD,SAAmD,CAAC;oBAEpD,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;wBAClB,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;qBACtB;oBAEgB,qBAAM,KAAK,CAAC,KAAK,CAAC,6BAA6B,CAAC,EAAA;;oBAA3D,QAAQ,GAAG,SAAgD;oBAEjE,wBAAe,CAAC,QAAQ,CAAC,GAAG,EAAE,6BAA6B,CAAC,CAAC;;;;SAChE,CAAC,CAAC;IAEH,EAAE,CAAC,mCAAmC,EAAE;;;;;oBAC7B,KAAK,GAAI,iBAAiB,CAAC,oBAAoB,EAAE,EAAC,YAAY,EAAE,EAAE,EAAC,CAAC,MAA/D,CAAgE;oBAC5E,KAAK,CAAC,QAAQ,CAAC,6BAA6B,CAAC,CAAC;;;;oBAG1C,qBAAM,KAAK,CAAC,KAAK,CAAC,6BAA6B,CAAC,EAAA;;oBAAhD,SAAgD,CAAC;oBACjD,aAAI,CAAC,4BAA4B,CAAC,CAAC;;;;;;;;SAE1C,CAAC,CAAC;AACP,CAAC,CAAC,CAAC"}