{"version": 3, "sources": ["../../@tiptap/extension-underline/src/underline.ts", "../../@tiptap/extension-underline/src/index.ts"], "sourcesContent": ["import { Mark, mergeAttributes } from '@tiptap/core'\n\nexport interface UnderlineOptions {\n  /**\n   * HTML attributes to add to the underline element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    underline: {\n      /**\n       * Set an underline mark\n       * @example editor.commands.setUnderline()\n       */\n      setUnderline: () => ReturnType\n      /**\n       * Toggle an underline mark\n       * @example editor.commands.toggleUnderline()\n       */\n      toggleUnderline: () => ReturnType\n      /**\n       * Unset an underline mark\n       * @example editor.commands.unsetUnderline()\n       */\n      unsetUnderline: () => ReturnType\n    }\n  }\n}\n\n/**\n * This extension allows you to create underline text.\n * @see https://www.tiptap.dev/api/marks/underline\n */\nexport const Underline = Mark.create<UnderlineOptions>({\n  name: 'underline',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'u',\n      },\n      {\n        style: 'text-decoration',\n        consuming: false,\n        getAttrs: style => ((style as string).includes('underline') ? {} : false),\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['u', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setUnderline:\n        () =>\n        ({ commands }) => {\n          return commands.setMark(this.name)\n        },\n      toggleUnderline:\n        () =>\n        ({ commands }) => {\n          return commands.toggleMark(this.name)\n        },\n      unsetUnderline:\n        () =>\n        ({ commands }) => {\n          return commands.unsetMark(this.name)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-u': () => this.editor.commands.toggleUnderline(),\n      'Mod-U': () => this.editor.commands.toggleUnderline(),\n    }\n  },\n})\n", "import { Underline } from './underline.js'\n\nexport * from './underline.js'\n\nexport default Underline\n"], "mappings": ";;;;;;AAqCO,IAAM,YAAY,KAAK,OAAyB;EACrD,MAAM;EAEN,aAAa;AACX,WAAO;MACL,gBAAgB,CAAC;IACnB;EACF;EAEA,YAAY;AACV,WAAO;MACL;QACE,KAAK;MACP;MACA;QACE,OAAO;QACP,WAAW;QACX,UAAU,CAAA,UAAW,MAAiB,SAAS,WAAW,IAAI,CAAC,IAAI;MACrE;IACF;EACF;EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,KAAK,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;EAC9E;EAEA,cAAc;AACZ,WAAO;MACL,cACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,QAAQ,KAAK,IAAI;MACnC;MACF,iBACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,WAAW,KAAK,IAAI;MACtC;MACF,gBACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,UAAU,KAAK,IAAI;MACrC;IACJ;EACF;EAEA,uBAAuB;AACrB,WAAO;MACL,SAAS,MAAM,KAAK,OAAO,SAAS,gBAAgB;MACpD,SAAS,MAAM,KAAK,OAAO,SAAS,gBAAgB;IACtD;EACF;AACF,CAAC;ACrFD,IAAO,gBAAQ;", "names": []}