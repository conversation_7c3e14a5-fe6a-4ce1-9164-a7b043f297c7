<template>
  <div class="property-panel">
    <div class="panel-header">
      <h3>属性面板</h3>
    </div>
    
    <div class="panel-content">
      <!-- 未选中任何对象 -->
      <div v-if="!selectedObject" class="empty-state">
        <el-icon class="empty-icon"><Select /></el-icon>
        <p>请选择一个对象来编辑属性</p>
      </div>
      
      <!-- 已选中对象 -->
      <div v-else class="property-sections">
        <!-- 基础属性 -->
        <div class="property-section">
          <div class="section-title">
            <el-icon><Setting /></el-icon>
            <span>基础属性</span>
          </div>
          
          <div class="property-grid">
            <!-- 位置 -->
            <div class="property-row">
              <label>位置</label>
              <div class="input-group">
                <el-input-number
                  v-model="properties.left"
                  :precision="0"
                  size="small"
                  controls-position="right"
                  @change="updateProperty('left', $event)"
                />
                <span class="input-separator">×</span>
                <el-input-number
                  v-model="properties.top"
                  :precision="0"
                  size="small"
                  controls-position="right"
                  @change="updateProperty('top', $event)"
                />
              </div>
            </div>
            
            <!-- 尺寸 -->
            <div class="property-row">
              <label>尺寸</label>
              <div class="input-group">
                <el-input-number
                  v-model="properties.width"
                  :precision="0"
                  :min="1"
                  size="small"
                  controls-position="right"
                  @change="updateProperty('width', $event)"
                />
                <span class="input-separator">×</span>
                <el-input-number
                  v-model="properties.height"
                  :precision="0"
                  :min="1"
                  size="small"
                  controls-position="right"
                  @change="updateProperty('height', $event)"
                />
              </div>
            </div>
            
            <!-- 旋转角度 -->
            <div class="property-row">
              <label>旋转</label>
              <el-input-number
                v-model="properties.angle"
                :precision="0"
                :min="-360"
                :max="360"
                size="small"
                controls-position="right"
                @change="updateProperty('angle', $event)"
              />
            </div>
            
            <!-- 透明度 -->
            <div class="property-row">
              <label>透明度</label>
              <el-slider
                v-model="properties.opacity"
                :min="0"
                :max="1"
                :step="0.1"
                :format-tooltip="(val) => Math.round(val * 100) + '%'"
                @change="updateProperty('opacity', $event)"
              />
            </div>
          </div>
        </div>
        
        <!-- 样式属性 -->
        <div class="property-section" v-if="showStyleProperties">
          <div class="section-title">
            <el-icon><Brush /></el-icon>
            <span>样式属性</span>
          </div>
          
          <div class="property-grid">
            <!-- 填充颜色 -->
            <div class="property-row" v-if="properties.fill !== undefined">
              <label>填充颜色</label>
              <el-color-picker
                v-model="properties.fill"
                size="small"
                @change="updateProperty('fill', $event)"
              />
            </div>
            
            <!-- 边框颜色 -->
            <div class="property-row" v-if="properties.stroke !== undefined">
              <label>边框颜色</label>
              <el-color-picker
                v-model="properties.stroke"
                size="small"
                @change="updateProperty('stroke', $event)"
              />
            </div>
            
            <!-- 边框宽度 -->
            <div class="property-row" v-if="properties.strokeWidth !== undefined">
              <label>边框宽度</label>
              <el-input-number
                v-model="properties.strokeWidth"
                :precision="0"
                :min="0"
                :max="20"
                size="small"
                controls-position="right"
                @change="updateProperty('strokeWidth', $event)"
              />
            </div>
          </div>
        </div>
        
        <!-- 文本属性 -->
        <div class="property-section" v-if="showTextProperties">
          <div class="section-title">
            <el-icon><EditPen /></el-icon>
            <span>文本属性</span>
          </div>
          
          <div class="property-grid">
            <!-- 文本内容 -->
            <div class="property-row">
              <label>文本内容</label>
              <el-input
                v-model="textProperties.text"
                type="textarea"
                :rows="3"
                size="small"
                @change="updateTextProperty('text', $event)"
              />
            </div>
            
            <!-- 字体大小 -->
            <div class="property-row">
              <label>字体大小</label>
              <el-input-number
                v-model="textProperties.fontSize"
                :precision="0"
                :min="8"
                :max="72"
                size="small"
                controls-position="right"
                @change="updateTextProperty('fontSize', $event)"
              />
            </div>
            
            <!-- 字体族 -->
            <div class="property-row">
              <label>字体</label>
              <el-select
                v-model="textProperties.fontFamily"
                size="small"
                @change="updateTextProperty('fontFamily', $event)"
              >
                <el-option label="Arial" value="Arial" />
                <el-option label="微软雅黑" value="Microsoft YaHei" />
                <el-option label="宋体" value="SimSun" />
                <el-option label="黑体" value="SimHei" />
                <el-option label="楷体" value="KaiTi" />
              </el-select>
            </div>
            
            <!-- 字体粗细 -->
            <div class="property-row">
              <label>字体粗细</label>
              <el-select
                v-model="textProperties.fontWeight"
                size="small"
                @change="updateTextProperty('fontWeight', $event)"
              >
                <el-option label="正常" value="normal" />
                <el-option label="粗体" value="bold" />
                <el-option label="细体" value="lighter" />
              </el-select>
            </div>
          </div>
        </div>
        
        <!-- 图片属性 -->
        <div class="property-section" v-if="showImageProperties">
          <div class="section-title">
            <el-icon><Picture /></el-icon>
            <span>图片属性</span>
          </div>
          
          <div class="property-grid">
            <div class="property-row">
              <label>图片源</label>
              <el-input
                v-model="imageProperties.src"
                placeholder="图片URL或上传图片"
                size="small"
                @change="updateImageProperty('src', $event)"
              />
            </div>
            
            <div class="property-row">
              <el-button size="small" @click="uploadImage">
                <el-icon><Upload /></el-icon>
                上传图片
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="property-section">
          <div class="section-title">
            <el-icon><Operation /></el-icon>
            <span>操作</span>
          </div>
          
          <div class="action-buttons">
            <el-button size="small" @click="duplicateObject">
              <el-icon><CopyDocument /></el-icon>
              复制
            </el-button>
            <el-button size="small" type="danger" @click="deleteObject">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Select,
  Setting,
  Brush,
  EditPen,
  Picture,
  Operation,
  Upload,
  CopyDocument,
  Delete
} from '@element-plus/icons-vue'

import type { CanvasObject } from '../types'

defineOptions({
  name: "PropertyPanel"
})

// Props
interface Props {
  selectedObject: CanvasObject | null
}

const props = defineProps<Props>()

// 事件定义
const emit = defineEmits<{
  'property-changed': [property: string, value: any]
  'duplicate-object': []
  'delete-object': []
}>()

// 响应式数据
const properties = ref({
  left: 0,
  top: 0,
  width: 0,
  height: 0,
  angle: 0,
  opacity: 1,
  fill: '#000000',
  stroke: '#000000',
  strokeWidth: 1
})

const textProperties = ref({
  text: '',
  fontSize: 16,
  fontFamily: 'Arial',
  fontWeight: 'normal'
})

const imageProperties = ref({
  src: '',
  alt: ''
})

// 计算属性
const showStyleProperties = computed(() => {
  return props.selectedObject && ['rectangle', 'circle', 'line'].includes(props.selectedObject.type)
})

const showTextProperties = computed(() => {
  return props.selectedObject && ['text', 'textbox'].includes(props.selectedObject.type)
})

const showImageProperties = computed(() => {
  return props.selectedObject && props.selectedObject.type === 'image'
})

// 监听选中对象变化
watch(() => props.selectedObject, (newObject) => {
  if (newObject) {
    // 更新基础属性
    properties.value = {
      left: newObject.left,
      top: newObject.top,
      width: newObject.width,
      height: newObject.height,
      angle: newObject.angle,
      opacity: newObject.opacity,
      fill: newObject.fill || '#000000',
      stroke: newObject.stroke || '#000000',
      strokeWidth: newObject.strokeWidth || 1
    }
    
    // 更新文本属性
    if (newObject.customProperties) {
      textProperties.value = {
        text: newObject.customProperties.text || '',
        fontSize: newObject.customProperties.fontSize || 16,
        fontFamily: newObject.customProperties.fontFamily || 'Arial',
        fontWeight: newObject.customProperties.fontWeight || 'normal'
      }
      
      imageProperties.value = {
        src: newObject.customProperties.src || '',
        alt: newObject.customProperties.alt || ''
      }
    }
  }
}, { immediate: true })

// 更新属性
const updateProperty = (property: string, value: any) => {
  emit('property-changed', property, value)
}

const updateTextProperty = (property: string, value: any) => {
  emit('property-changed', property, value)
}

const updateImageProperty = (property: string, value: any) => {
  emit('property-changed', property, value)
}

// 上传图片
const uploadImage = () => {
  // TODO: 实现图片上传功能
  ElMessage.info('图片上传功能待实现')
}

// 复制对象
const duplicateObject = () => {
  emit('duplicate-object')
}

// 删除对象
const deleteObject = () => {
  emit('delete-object')
}
</script>

<style lang="scss" scoped>
.property-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color);

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.panel-content {
  flex: 1;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--el-text-color-placeholder);

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}

.property-sections {
  padding: 16px;
}

.property-section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-regular);
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.property-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.property-row {
  display: flex;
  flex-direction: column;
  gap: 6px;

  label {
    font-size: 12px;
    color: var(--el-text-color-regular);
    font-weight: 500;
  }

  .input-group {
    display: flex;
    align-items: center;
    gap: 8px;

    .input-separator {
      font-size: 12px;
      color: var(--el-text-color-placeholder);
    }

    .el-input-number {
      flex: 1;
    }
  }
}

.action-buttons {
  display: flex;
  gap: 8px;

  .el-button {
    flex: 1;
  }
}

// 覆盖 Element Plus 样式
:deep(.el-input-number) {
  width: 100%;

  .el-input__inner {
    text-align: left;
  }
}

:deep(.el-slider) {
  margin: 8px 0;
}

:deep(.el-color-picker) {
  width: 100%;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-textarea) {
  .el-textarea__inner {
    resize: vertical;
  }
}
</style>
